/* Query Container */
.query-container {
  max-width: 600px;
  margin: 20px auto;
  padding: 24px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.query-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24px;
  text-align: center;
  border-bottom: 2px solid #2196f3;
  padding-bottom: 8px;
}

.criteria-row {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  gap: 14px;
}

.field-label {
  width: 130px;
  font-weight: 600;
  color: #555555;
  text-align: right;
}

.operator-select,
.value-input {
  flex: 1;
  font-size: 0.95rem;
}

.operator-select {
  max-width: 120px;
}

.value-input {
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fdfdfd;
  transition: border 0.3s ease;
}

.value-input:focus {
  border-color: #2196f3;
  box-shadow: 0 0 3px rgba(33, 150, 243, 0.5);
}

.submit-button {
  width: 100%;
  margin-top: 24px;
  padding: 12px;
  font-weight: 600;
  background-color: #2196f3;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.submit-button:hover {
  background-color: #1b82d6;
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.25);
}

/* Results Container */
.results-container {
  max-width: 850px;
  margin: 30px auto;
  padding: 20px;
  background-color: #f7f7f7;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.download-button {
  margin-bottom: 16px;
  padding: 10px 20px;
  background-color: #388e3c;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.download-button:hover {
  background-color: #2e7030;
}

/* Table Styles */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

th,
td {
  padding: 14px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  font-size: 0.95rem;
}

th {
  background-color: #f1f1f1; /* Slightly lighter background */
  color: #333333; /* Darker text color for readability */
  font-weight: 600;
  text-transform: uppercase;
  border-bottom: 2px solid #2196f3;
}

td {
  background-color: #ffffff;
  color: #555555;
}

tr:hover td {
  background-color: #f5f5f5;
  transition: background-color 0.2s ease;
}
