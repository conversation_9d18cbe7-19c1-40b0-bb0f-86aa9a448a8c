.set-password-page {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #2d4f86, #34a853); // Same gradient background as login page

  .set-password-container {
    width: 100%;
    max-width: 400px;
    padding: 40px;
    background-color: #ffffff;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    text-align: center;
    animation: fadeIn 1s ease-in-out;

    .logo {
      width: 150px;
      margin-bottom: 20px;
    }

    h2 {
      font-size: 2em;
      font-weight: bold;
      margin-bottom: 20px;
      color: #2d4f86; // Logo's blue for title color
    }

    form {
      display: flex;
      flex-direction: column;
      align-items: center;

      label {
        margin-bottom: 5px;
        font-size: 0.9em;
        color: #666666;
        align-self: flex-start;
      }

      input[type="password"] {
        width: 100%;
        padding: 12px;
        margin-bottom: 15px;
        border: 1px solid #cccccc;
        border-radius: 8px;
        font-size: 1em;
        color: #333333;
        background-color: #f9f9f9;
        transition: border-color 0.3s;
        
        &:focus {
          border-color: #2d4f86;
          outline: none;
        }
      }

      button {
        width: 100%;
        padding: 12px;
        background-color: #34a853; // Green from the logo
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 1em;
        font-weight: bold;
        text-transform: uppercase;
        transition: background-color 0.3s;

        &:hover {
          background-color: #2d4f86; // Logo's blue for hover effect
        }
      }
    }

    .error-message, .success-message {
      font-size: 0.9em;
      font-weight: bold;
      text-align: center;
      margin-bottom: 15px;
    }

    .error-message {
      color: #d9534f; // Red for error messages
    }

    .success-message {
      color: #5cb85c; // Green for success messages
    }
  }

  .loading-overlay { 
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .loading-spinner {
    border: 4px solid #f3f3f3; 
    border-radius: 50%;
    border-top: 4px solid #3498db; 
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}