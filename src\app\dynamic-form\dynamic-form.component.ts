import { Component, Input, OnInit, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit } from "@angular/core";
import { FormBuilder, FormGroup, FormArray, Validators, AbstractControl } from "@angular/forms";
import { MetadataService } from "../metadata.service";
import { HttpClient } from "@angular/common/http";
import { CommonModule } from "@angular/common";
import { ReactiveFormsModule } from "@angular/forms";
import Choices from 'choices.js';

@Component({
  selector: "app-dynamic-form",
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: "./dynamic-form.component.html",
  styleUrls: ["./dynamic-form.component.scss"],
})
export class DynamicFormComponent implements OnInit, AfterViewInit {
  @Input() tableName!: string;
  @Input() data: any;
  @Output() dataChange = new EventEmitter<any>();
  @ViewChild('mySelect') mySelect!: ElementRef;

  form!: FormGroup;
  fields: any[] = [];
  submissionSuccess = false;
  errorMessage = "";
  isLoading = false;
  showInitialInput = true;
  isViewMode = false;
  successMessage: string = "";
  showSuccessPopup = false;
  dropdownOptions: { [key: string]: any[] } = {};
  dropdownLoaded: { [key: string]: boolean } = {};
  dropdownVisible: { [key: string]: boolean } = {};
  validationResult: any; 
   

  constructor(
    private metadataService: MetadataService,
    private fb: FormBuilder,
    private http: HttpClient
  ) {}

  ngOnInit() {
    if (this.tableName) {
      this.initializeForm();
    }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.fields.forEach((field) => {
        if (field.foreginKey) {
          const selectElement = document.getElementById(field.fieldName);
          if (selectElement) {
            new Choices(selectElement, {
              searchEnabled: true,
              itemSelectText: '',
            });
          }
        }
      });
    }, 200); 
  }

  initializeForm() {
    this.form = this.fb.group({
      ID: ["", Validators.required],
    });
  }

  setFormReadonly(isReadonly: boolean) {
    const disableControls = (control: AbstractControl) => {
      if (control instanceof FormGroup) {
        Object.values(control.controls).forEach(disableControls);
      } else if (control instanceof FormArray) {
        control.controls.forEach(disableControls);
      } else {
        if (isReadonly) {
          control.disable({ emitEvent: false });
        } else {
          control.enable({ emitEvent: false });
        }
      }
    };

    disableControls(this.form); // Apply disable logic to the entire form

    // Explicitly handle isMulti fields
    this.fields.forEach((field) => {
      if (field.isMulti) {
        const formArray = this.form.get(field.fieldName) as FormArray;
        if (formArray) {
          formArray.controls.forEach((control) => {
            if (isReadonly) {
              control.disable({ emitEvent: false });
            } else {
              control.enable({ emitEvent: false });
            }
          });
        }
      }

      // Explicitly handle grouped fields
      if (field.Group) {
        const groupArray = this.getGroupArray(field.Group);
        if (groupArray) {
          groupArray.controls.forEach((control) => {
            if (isReadonly) {
              control.disable({ emitEvent: false });
            } else {
              control.enable({ emitEvent: false });
            }
          });
        }
      }
    });
  }

  viewData() {
    this.isViewMode = true;
    this.loadDataAndBuildForm();
    setTimeout(() => {
      this.setFormReadonly(true); // Apply readonly after form is built
    }, 0);
  }

  loadDataAndBuildForm() {
    this.isLoading = true;
    this.errorMessage = "";
    this.successMessage = "";
    const id = this.form.get("ID")?.value;
    const apiUrl = ` http://localhost:5553/api/validation/validate-id?tableName=${this.tableName}&id=${id}`;

    this.http.get(apiUrl, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (response.success) {
          this.showInitialInput = false;
          this.loadTableMetadata();
        } else {
          this.errorMessage = response.message || "ID validation failed";
          this.isLoading = false;
        }
      },
      error: (error) => {
        this.errorMessage = "Error validating ID";
        console.error("Error:", error);
        this.isLoading = false;
      },
      complete: () => this.isLoading = false 
    });
  }

  loadTableMetadata() {
    this.isLoading = true;
    this.metadataService.getTableMetadata(this.tableName).subscribe({
      next: (response: any) => {
        if (response?.data?.fieldName) {
          this.fields = response.data.fieldName;
          this.buildForm();
          this.fetchFormData();
        } else {
          console.error("Invalid metadata response:", response);
          this.isLoading = false;
        }
      },
      error: (error) => {
        console.error("Error fetching metadata:", error);
        this.isLoading = false;
      },
      complete: () => this.isLoading = false 
    });
  }

  buildForm() {
    const groupedFields: { [key: string]: FormArray } = {};

    this.fields.forEach((field) => {
      if (field.fieldName !== "ID") {
        if (field.isMulti) {
          this.form.addControl(
            field.fieldName,
            this.fb.array([this.createMultiField(field)]) 
          );
        } else if (field.Group) {
          if (!groupedFields[field.Group]) {
            groupedFields[field.Group] = this.fb.array([]);
            this.form.addControl(field.Group, groupedFields[field.Group]);
            this.addGroup(field.Group);
          }
        } else {
          const validators = field.mandatory ? Validators.required : null;
          let control;
          switch (field.type) {
            case "boolean":
              control = this.fb.control(false, validators);
              break;
            case "date":
              control = this.fb.control(null, validators); 
              break;
            default:
              control = this.fb.control("", validators);
              break;
          }
          this.form.addControl(field.fieldName, control);
          if (field.foreginKey) {
            this.dropdownLoaded[field.fieldName] = false;
          }
        }
      }
    });
  }

  loadDropdownOptions(fieldName: string, foreignKey: string, groupIndex?: number, multiIndex?: number, groupName?: string) { 
    if (this.dropdownLoaded[fieldName]) {
      return;
    }

    const apiUrl = ` http://localhost:5553/api/query-builder/search?queryBuilderId=${foreignKey}`;
    this.http.get(apiUrl, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.dropdownOptions[fieldName] = response;
          this.dropdownLoaded[fieldName] = true;

          if (groupIndex !== undefined && multiIndex !== undefined && groupName) { 
            const multiArray = this.getMultiArray(fieldName, groupIndex, groupName);
            const control = multiArray.at(multiIndex) as FormGroup;
            control.get(fieldName)?.setValue(response[0]?.ROW_ID || "");
          }
        } else {
          console.error("Unexpected API response:", response);
        }
      },
      error: (error) => {
        console.error("Error fetching dropdown options:", error);
      }
    });
  }

  createGroup(groupName: string): FormGroup {
    const group = this.fb.group({});
    this.getFieldsForGroup(groupName).forEach((field) => {
      if (field.isMulti) {
        group.addControl(
          field.fieldName,
          this.fb.array([this.createMultiField(field)])
        );
      } else if (field.foreginKey) {
        group.addControl(
          field.fieldName,
          this.fb.control("", field.mandatory ? Validators.required : null)
        );
        this.dropdownLoaded[field.fieldName] = false;
      } else {
        group.addControl(
          field.fieldName,
          this.fb.control("", field.mandatory ? Validators.required : null)
        );
      }
    });
    return group;
  }

  createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    if (Array.isArray(field)) {
      field.forEach(fieldName => {
        group.addControl(fieldName, this.fb.control('', Validators.required));
      });
    } else {
      group.addControl(field.fieldName, this.fb.control("", field.mandatory ? Validators.required : null));
    }
    return group;
  }

  getFieldsForGroup(groupName: string) {
    return this.fields.filter((field) => field.Group === groupName);
  }

  getGroupArray(groupName: string): FormArray {
    return this.form.get(groupName) as FormArray;
  }

  addGroup(groupName: string, index?: number) {
    const groupArray = this.getGroupArray(groupName);
    const newGroup = this.createGroup(groupName);

    if (index !== undefined) {
      groupArray.insert(index + 1, newGroup);
    } else {
      groupArray.push(newGroup);
    }
  }

  removeGroup(groupName: string, index: number) {
    this.getGroupArray(groupName).removeAt(index);
  }

  getMultiArray(fieldName: string, groupIndex?: number, groupName?: string): FormArray {
    if (groupIndex !== undefined && groupName) {
      const groupArray = this.getGroupArray(groupName);
      const group = groupArray.at(groupIndex) as FormGroup;
      return group.get(fieldName) as FormArray;
    } else {
      return this.form.get(fieldName) as FormArray;
    }
  }

  addMultiField(field: any, groupIndex?: number, index?: number, groupName?: string) {
    try {
      const multiArray = this.getMultiArray(field.fieldName, groupIndex, groupName);
      const newField = this.createMultiField(field);
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }

    } catch (error) {
      console.error("Error adding multi-field:", error);
    }
  }

  removeMultiField(fieldName: string, index: number, groupIndex?: number, groupName?: string) {
    const multiArray = this.getMultiArray(fieldName, groupIndex, groupName);
    multiArray.removeAt(index);
  }

  isFirstFieldInGroup(field: any): boolean {
    return (
      this.fields.findIndex((f) => f.Group === field.Group) ===
      this.fields.indexOf(field)
    );
  }

  trackByFieldName(index: number, field: any): string {
    return field.fieldName;
  }

  authorizeRecord() {
    this.errorMessage = "";
    this.isLoading = true;

    const id = this.form.get("ID")?.value;
    const apiUrl = ` http://localhost:5553/api/tables/${this.tableName}/records/${id}/authorize`;

    this.http.put(apiUrl, {}, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (response && response.status === "success") {
          this.showSuccessPopup = true;
          this.successMessage = "Record authorized successfully!";
          this.isViewMode = false;
          this.setFormReadonly(false);
          this.goBack();
          setTimeout(() => {
            this.showSuccessPopup = false;
          }, 20000);
        } else {
          this.errorMessage = response.message || "Authorization failed";
        }
      },
      error: (error) => {
        this.errorMessage = "An error occurred during authorization";
        console.error("Authorization Error:", error);
      },
      complete: () => this.isLoading = false
    });
  }

  onSubmit() {
    this.errorMessage = "";

    if (this.form.valid) {
      const apiUrl = ` http://localhost:5553/api/tables/${this.tableName}/records`;
      const formData = this.buildFormData(this.form.value);

      this.http.post(apiUrl, formData, { withCredentials: true }).subscribe({
        next: (response: any) => {
          if (response.status === "success") {
            this.showSuccessPopup = true;
            this.successMessage = "Record submitted successfully!";
            this.goBack();
            setTimeout(() => {
              this.showSuccessPopup = false;
            }, 20000);
          } else if (response.status === "error") {
            this.errorMessage = response.message || "An error occurred while submitting the form";
          }
        },
        error: (error: any) => {
          if (error.error && error.error.message) {
            this.errorMessage = error.error.message;
          } else {
            this.errorMessage = "An unexpected error occurred while submitting the form";
            console.error("Submission Error:", error);
          }
        }
      });
    } else {
      console.error("Form is invalid");
    }
  }

  validateRecord() {
    this.errorMessage = "";
    this.isLoading = true;
  
    if (this.form.valid) {
      const apiUrl = `http://localhost:5553/api/tables/${this.tableName}/validate`;
      const formData = this.buildFormData(this.form.value);
  
      this.http.post(apiUrl, formData, { withCredentials: true }).subscribe({
        next: (response: any) => {
          if (response.status === "success") {
            this.validationResult = response.data;
            this.populateForm(response.data);
          } else if (response.status === "error") {
            this.validationResult = response.data;
            this.errorMessage = response.message || "An error occurred during validation";
            this.populateForm(response.data);
          }
        },
        error: (error: any) => {
          this.validationResult = error.error?.data || null;
          this.errorMessage = error.error?.message || "An unexpected error occurred during validation";
          console.error("Validation Error:", error);
  
          if (this.validationResult) {
            this.populateForm(this.validationResult);
          }
        },
        complete: () => this.isLoading = false
      });
    } else {
      console.error("Form is invalid");
      this.isLoading = false;
    }
  }
  
  closeSuccessPopup() {
    this.showSuccessPopup = false;
  }

  private buildFormData(data: any): any {
    const result: { [key: string]: any } = {};
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const value = data[key];
        if (value === null || value === undefined || value === "") continue;

        if (Array.isArray(value)) {
          const field = this.fields.find(field => field.fieldName === key);
          if (field && field.isMulti) {
            if (typeof value[0] === 'object' && value[0][field.fieldName] !== undefined) {
              result[key] = value.map((item: any) => item[field.fieldName]);
            } else {
              result[key] = value;
            }
          } else {
            const nestedData = value.map((item: any) => {
              if (item.fieldsToAppear) {
                return item.fieldsToAppear;
              } else {
                return this.buildFormData(item);
              }
            });
            if (nestedData.length > 0) {
              result[key] = nestedData;
            }
          }
        } else if (typeof value === "object") {
          const nestedObject = this.buildFormData(value);
          if (Object.keys(nestedObject).length > 0) {
            result[key] = nestedObject;
          }
        } else {
          result[key] = value;
        }
      }
    }
    return result;
  }

  fetchFormData() {
    this.isLoading = true;
    const id = this.form.get("ID")?.value;
    const apiUrl = ` http://localhost:5553/api/tables/${this.tableName}/records/${id}`;

    this.http.get(apiUrl, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (response && response.data) {
          this.populateForm(response.data);
        } else {
          console.warn("Data not found in API response:", response);
        }
      },
      error: (error) => {
        this.errorMessage = "An error occurred while fetching data";
        console.error("Error fetching data:", error);
      },
      complete: () => this.isLoading = false
    });
  }

  populateForm(data: any) {
    Object.keys(data).forEach(key => {
      const formControl = this.form.get(key);

      if (formControl instanceof FormArray && Array.isArray(data[key])) {
        const formArray = formControl as FormArray;
        formArray.clear(); // Clear existing controls in the FormArray

        if (this.fields.some(field => field.Group === key)) {
          // Handle Group fields 
          data[key].forEach((groupData: any) => {
            formArray.push(this.createGroup(key)); 
            (formArray.at(formArray.length - 1) as FormGroup).patchValue(groupData);
          });
        } else {
          // Handle multi-fields 
          const field = this.fields.find(field => field.fieldName === key);
          if (field) {
            // Create form groups in the FormArray
            for (let i = 0; i < data[key].length; i++) {
              formArray.push(this.createMultiField(field));
            }

            // Patch the values
            data[key].forEach((value: any, index: number) => {
              const newGroup = formArray.at(index) as FormGroup;

              // Check if the value is an object (for multi-fields with multiple properties)
              if (typeof value === 'object' && !Array.isArray(value)) {  
                newGroup.patchValue(value); 
              } else {
                // If the value is not an object, patch it to the field.fieldName
                newGroup.patchValue({ [field.fieldName]: value }); 
              }
            });
          } else {
            console.warn(`Field definition not found for key: ${key}`);
          }
        }
      } else if (formControl) {
        // For simple fields (not FormArray)
        const field = this.fields.find(field => field.fieldName === key);
        if (field && field.type === 'date' && typeof data[key] === 'string') {
          const parsedDate = new Date(data[key]);
          if (!isNaN(parsedDate.getTime())) {
            formControl.setValue(parsedDate);
          } else {
            console.error('Invalid date string:', data[key]);
            formControl.setValue(null);
          }
        } else if (field && field.type === 'date' && Array.isArray(data[key])) {
          // Handle the case where data[key] is an array of date strings (for multi-fields)
          const parsedDates = data[key].map(dateStr => {
            const parsedDate = new Date(dateStr);
            return !isNaN(parsedDate.getTime()) ? parsedDate : null;
          });
          formControl.setValue(parsedDates);
        } else {
          formControl.setValue(data[key]);
        }

        if (this.isViewMode) {
          formControl.disable(); // Disable the control AFTER setting the value
        }
      }
    });
  }

  // goBack() {
  //   this.showInitialInput = true;
  //   this.form.reset();
  //   this.fields = [];
  //   this.isViewMode = false;
  //   this.submissionSuccess = false;
  //   this.validationResult = null;
  //   this.setFormReadonly(false);
  // }

  goBack() {
    const id = this.form.get("ID")?.value;
    const apiUrl = `http://localhost:5553/api/tables/${this.tableName}/records/${id}/force-unlock`;
  
    // إرسال طلب POST إلى الـ API لإلغاء القفل
    this.http.delete(apiUrl, { withCredentials: true }).subscribe({
      next: () => {
        console.log("Record successfully unlocked.");
      },
      error: (error) => {
        console.error("Error during force-unlock:", error);
        this.showInitialInput = true;
        this.form.reset();
        this.fields = [];
        this.isViewMode = false;
        this.submissionSuccess = false;
        this.validationResult = null;
        this.setFormReadonly(false);
      },
      complete: () => {
        // إعادة الحالة الافتراضية للفورم بعد إلغاء القفل
        this.showInitialInput = true;
        this.form.reset();
        this.fields = [];
        this.isViewMode = false;
        this.submissionSuccess = false;
        this.validationResult = null;
        this.setFormReadonly(false);
      }
    });
  }
  onDropdownChange(event: Event, fieldName: string, groupIndex?: number, multiIndex?: number, groupName?: string) {
    const value = (event.target as HTMLSelectElement).value;

    if (groupName && groupIndex !== undefined) {
      const groupArray = this.getGroupArray(groupName);
      const group = groupArray.at(groupIndex) as FormGroup;

      if (multiIndex !== undefined) {
        const multiArray = group.get(fieldName) as FormArray;
        const multiControl = multiArray.at(multiIndex) as FormGroup;

        if (multiControl instanceof FormGroup) {
          multiControl.setValue({ [fieldName]: value });
        } else {
          console.error(`Control at index ${multiIndex} in ${fieldName} is not a FormGroup`);
        }
      } else {
        const groupControl = group.get(fieldName);
        if (groupControl) {
          groupControl.setValue(value);
        } else {
          console.error(`Control ${fieldName} not found in group ${groupName} at index ${groupIndex}`);
        }
      }
    } else {
      const control = this.form.get(fieldName);

      if (multiIndex !== undefined && control instanceof FormArray) {
        const multiArray = control as FormArray;
        const multiControl = multiArray.at(multiIndex) as FormGroup;

        if (multiControl instanceof FormGroup) {
          multiControl.setValue({ [fieldName]: value });
        } else {
          console.error(`Control at index ${multiIndex} in ${fieldName} is not a FormGroup`);
        }
      } else if (control) {
        control.setValue(value);
      } else {
        console.error(`Control ${fieldName} not found in the main form`);
      }
    }
  }

  getKeys(option: any): string[] {
    return Object.keys(option);
  }

  toggleDropdown(fieldName: string) {
    this.dropdownVisible[fieldName] = !this.dropdownVisible[fieldName];
  }


  cloneGroup(groupName: string, index: number): void {
  const groupArray = this.getGroupArray(groupName);
  const groupToClone = groupArray.at(index) as FormGroup;

  // Step 1: Add a new empty group using your existing method
  this.addGroup(groupName, index);

  // Step 2: Get the newly inserted group
  const clonedGroup = groupArray.at(index + 1) as FormGroup;

  // Step 3: Copy values (deep clone including nested FormArrays)
  Object.keys(groupToClone.controls).forEach(key => {
    const control = groupToClone.get(key);
    const clonedControl = clonedGroup.get(key);

    if (control instanceof FormArray && clonedControl instanceof FormArray) {
      clonedControl.clear();
      control.controls.forEach(c => {
        const clonedSubGroup = this.fb.group({});
        Object.keys((c as FormGroup).controls).forEach(subKey => {
          clonedSubGroup.addControl(subKey, this.fb.control((c as FormGroup).get(subKey)?.value));
        });
        clonedControl.push(clonedSubGroup);
      });
    } else {
      clonedControl?.setValue(control?.value);
    }
  });
}


}
