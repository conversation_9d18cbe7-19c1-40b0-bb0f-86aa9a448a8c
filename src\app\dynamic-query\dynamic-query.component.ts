import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatTableModule } from '@angular/material/table';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

@Component({
  selector: 'app-dynamic-query',
  templateUrl: './dynamic-query.component.html',
  styleUrls: ['./dynamic-query.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatOptionModule,
    MatTableModule
  ]
})
export class DynamicQueryComponent implements OnInit {
  @Input() queryName!: string;
  criteriaFields: any[] = []; 
  formValues: { [key: string]: any } = {}; 
  results: any[] = []; 
  columns: string[] = []; 
  showTable: boolean = false; 
  @Input() data: any;
  @Output() dataChange = new EventEmitter<any>();
  constructor(private http: HttpClient, private cdr: ChangeDetectorRef) {}

  ngOnInit() {
    if (this.queryName) {
      this.fetchCriteriaFields();
    }
  }

  fetchCriteriaFields() {
    this.http.get<any[]>(`http://localhost:5553/api/query-builder/${this.queryName}`, { withCredentials: true }).subscribe(
      (response) => {
        this.criteriaFields = response;
        this.initializeFormValues();
        this.cdr.detectChanges();
      },
      (error) => {
        console.error('Error fetching query criteria:', error);
      }
    );
  }

  initializeFormValues() {
    this.criteriaFields.forEach(field => {
      this.formValues[field.selectionField] = '';
      this.formValues[field.selectionField + '_operator'] = field.operator || 'EQ';
    });
  }

  formatCriteria(): string {
    const formattedCriteria = this.criteriaFields.map(field => {
      const value = this.formValues[field.selectionField];
      const operator = this.formValues[field.selectionField + '_operator'];
      if (value) {
        return `${field.selectionField} ${operator} "${value}"`;
      }
      return '';
    }).filter(criterion => criterion).join(' &&& ');
    return formattedCriteria;
  }

  submitQuery() {
    const criteria = this.formatCriteria();
    const url = `http://localhost:5553/api/query-builder/search?queryBuilderId=${this.queryName}&criteria=${encodeURIComponent(criteria)}`;
    
    this.http.get<any[]>(url, { withCredentials: true }).subscribe(
      (response) => {
        this.results = response;
        if (this.results.length > 0) {
          this.columns = Object.keys(this.results[0]);
          this.showTable = true; 
        }
        console.log('API Results:', this.results);
      },
      (error) => {
        console.error('Error fetching query results:', error);
      }
    );
  }

  downloadPDF() {
    const doc = new jsPDF();
    doc.text('Query Results', 14, 10);
    (doc as any).autoTable({
      head: [this.columns],
      body: this.results.map(row => this.columns.map(col => row[col])),
      startY: 20,
      theme: 'grid'
    });
    doc.save('query-results.pdf');
  }
}