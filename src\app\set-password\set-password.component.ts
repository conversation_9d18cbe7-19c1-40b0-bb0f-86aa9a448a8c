// set-password.component.ts
import { Component, OnInit } from '@angular/core';
import { AuthenticationService } from '../authentication.service';
import { Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-set-password',
  templateUrl: './set-password.component.html',
  styleUrls: ['./set-password.component.scss'],
  standalone: true,
  imports: [FormsModule, NgIf]
})
export class SetPasswordComponent implements OnInit {
  newPassword: string = '';
  confirmPassword: string = '';
  errorMessage: string = '';
  successMessage: string = '';
  username: string = '';
  isLoading: boolean = false; 

  constructor(
    private authenticationService: AuthenticationService, 
    private router: Router
  ) {}

  ngOnInit(): void {
    this.username = sessionStorage.getItem('tempUsername') || '';
    sessionStorage.removeItem('tempUsername'); 
  }

  onSubmit() {
    this.errorMessage = '';
    this.successMessage = '';

    if (this.newPassword !== this.confirmPassword) {
      this.errorMessage = 'Passwords do not match.';
      return;
    }

    const requestData = {
      username: this.username,
      password: this.newPassword,
      confirmPassword: this.confirmPassword
    };

    this.authenticationService.setPassword(requestData).subscribe({
      next: (response) => {
        if (response.success) {
          this.successMessage = response.message || 'Password has been set successfully!';
          this.isLoading = true; // Show loading animation

          setTimeout(() => {
            this.isLoading = false; // Hide loading animation
            this.router.navigate(['/login']); 
          }, 2000); 
        } else { 
          this.errorMessage = response.message || 'Failed to set password. Please try again.';
        }
      },
      error: (error) => {
        this.errorMessage = error.error?.message || 'An error occurred. Please try again later.';
        console.error('Set password failed:', error);
      }
    });
  }
}