<div class="login-page">
  <div class="login-container">
    <img src="assets/images/offical-logo-2.png" alt="Ultimate Solutions Logo" class="logo">
    <h2>Sign In</h2>

    <!-- Error Message Display -->
    <div *ngIf="errorMessage" class="error-message">
      <i class="fas fa-exclamation-circle"></i> {{ errorMessage }}
    </div>

    <form (ngSubmit)="onSubmit(loginForm)" #loginForm="ngForm">
      <div class="input-group">
        <label for="username">User Name</label>
        <input type="text" id="username" name="username" required ngModel>
      </div>
      <div class="input-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" required ngModel>
      </div>

      <div class="remember-me">
        <input type="checkbox" id="rememberMe" name="rememberMe">
        <label for="rememberMe">Remember Me</label>
      </div>

      <button type="submit">Sign In</button>

      <a href="#" class="forgot-password">Forgot Password?</a>
    </form>
  </div>
</div>
