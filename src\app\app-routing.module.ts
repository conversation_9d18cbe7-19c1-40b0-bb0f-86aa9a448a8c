// app-routing.module.ts
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { HomeComponent } from './home/<USER>';
import { SetPasswordComponent } from './set-password/set-password.component'; // Import the component
import { SetPasswordGuard } from './auth/set-password.guard';

const routes: Routes = [
  { path: 'login', component: LoginComponent },
  { path: 'home', component: HomeComponent },
  { path: '', redirectTo: '/login', pathMatch: 'full' }, // Fixed the closing bracket here
  { path: 'set-password', component: SetPasswordComponent, canActivate: [SetPasswordGuard] },
  { path: '**', redirectTo: '/login' } // Wildcard route for undefined paths
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule {}
