import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class SetPasswordGuard implements CanActivate {
  constructor(private router: Router) {}

  canActivate(): boolean {
    const canAccessSetPassword = localStorage.getItem('canAccessSetPassword');

    if (canAccessSetPassword === 'true') {
      // Allow access and remove the flag after use
      localStorage.removeItem('canAccessSetPassword');
      return true;
    } else {
      // Redirect to login if the flag is not set
      this.router.navigate(['/login']);
      return false;
    }
  }
}
