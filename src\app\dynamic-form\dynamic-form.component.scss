body {
  font-family: 'Poppins', sans-serif;
  color: #333;
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.button-group {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-direction: row-reverse;
  margin-left: auto;
}

.action-button, .submit-button {
  background-color: #007bff;
  color: #fff;
  font-weight: bold;
  padding: 6px 12px;
  font-size: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.action-button:hover, .submit-button:hover {
  background-color: #0069d9;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

.multi-field, .group-fields {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  background-color: #e8e8e8;
  margin-bottom: 16px;

}

/* Form Fields */
.form-field {
  margin-bottom: 15px;
 // width: 100%;
}

.form-field label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  font-size: 14px;
  color: #495057;
}

.form-field input,
.form-field select {
 // width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  width: 533px;
}

.form-field input:focus,
.form-field select:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #666;
}

h3 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #444;
}

.add-button, .remove-button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 12px;
  color: #000000;
  transition: color 0.3s ease;
}

.add-button:hover {
  color: #0056b3;
}

.remove-button {
  color: #dc3545;
}

.remove-button:hover {
  color: #c82333;
}

.error {
  color: #dc3545;
  font-size: 11px;
  margin-top: 4px;
}

.success-message {
  color: #28a745;
  font-size: 14px;
  font-weight: bold;
  padding: 10px;
  border: 1px solid #c3e6cb;
  background-color: #d4edda;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .form-grid {
    padding: 12px;
  }

  .form-field input {
  //  width: 100%;
    font-size: 14px;
  }

  .group-fields {
    flex-direction: column;
    gap: 10px;
  }
}
.error-message {
  background-color: #fdd; /* Light red background */
  border: 1px solid #faa; /* Red border */
  color: #a00; /* Dark red text color */
  padding: 10px;
  margin-bottom: 10px; 
  border-radius: 4px; /* Slightly rounded corners */
}
/* Add styles for the popup */
.popup {
  position: fixed; 
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%; 
  max-width: 400px; 
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5); 
  z-index: 100; 
}

.popup-content {
  text-align: center;
}

.close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 20px;
  cursor: pointer;
}

.dropdown-input-wrapper {
  display: flex;
  align-items: center;
}

.dropdown-input {
  flex: 1;
  padding: 0.5em;
  border: 1px solid #ccc;
  border-radius: 4px 0 0 4px;
}

.dropdown-icon {
  flex-shrink: 0;
  padding: 0.5em;
  border: 1px solid #ccc;
  border-left: none;
  border-radius: 0 4px 4px 0;
  background-color: #f1f1f1;
  cursor: pointer;
}
// add OS Marhabi ----------------------------------------



.form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.form-main-field {
  display: flex;
  align-items: center;
  gap: 24px;
  width: 100%;
  padding-top: 16px;
}

.form-label {
  width: 160px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #222222;
  padding-left: 24px;
}

.input-button-group {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-grow: 1;
  padding-right: 24px;
}

.form-input {
  flex-grow: 1;
  height: 40px;
 // padding: 8px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center; /* <<< مهم جدا عشان الايقونة تيجي بالنص */
  width: 40px;
  height: 40px;
  border: 1px solid #283A97;
  border-radius: 4px;
  background-color: white;
  color: #283A97;
  font-family: 'Poppins', sans-serif;
  font-size: 12px; /* عشان الايقونة تكون واضحة */
  cursor: pointer;
  transition: background-color 0.3s;
  padding: 0; /* <<< بدون padding */
}

.action-button:hover {
  background-color: #ebedf1;
  //color: white;
   border:0px;
}

.green-button {
  display: flex;
  align-items: center;
  justify-content: center; /* <<< مهم جدا عشان الايقونة تيجي بالنص */
  width: 40px;
  height: 40px;
  border: 1px solid #0DB14B;
  border-radius: 4px;
  background-color: white;
  color: #0DB14B;
  font-family: 'Poppins', sans-serif;
  font-size: 12px; /* عشان الايقونة تكون واضحة */
  cursor: pointer;
  transition: background-color 0.3s;
  padding: 0; /* <<< بدون padding */
}

.green-button:hover {
  background-color: #ebedf1;
  //color: white;
   border:0px;
}

.red-button {
  display: flex;
  align-items: center;
  justify-content: center; /* <<< مهم جدا عشان الايقونة تيجي بالنص */
  width: 40px;
  height: 40px;
  border: 1px solid #EB6F62;
  border-radius: 4px;
  background-color: white;
  color: #EB6F62;
  font-family: 'Poppins', sans-serif;
  font-size: 12px; /* عشان الايقونة تكون واضحة */
  cursor: pointer;
  transition: background-color 0.3s;
  padding: 0; /* <<< بدون padding */
}

.red-button:hover {
  background-color: #ebedf1;
  //color: white;
   border:0px;
}

.black-button {
  display: flex;
  align-items: center;
  justify-content: center; /* <<< مهم جدا عشان الايقونة تيجي بالنص */
  width: 40px;
  height: 40px;
  border: 1px solid #4D4D4D;
  border-radius: 4px;
  background-color: white;
  color: #4D4D4D;
  font-family: 'Poppins', sans-serif;
  font-size: 12px; /* عشان الايقونة تكون واضحة */
  cursor: pointer;
  transition: background-color 0.3s;
  padding: 0; /* <<< بدون padding */
}

.black-button:hover {
  background-color: #ebedf1;
  //color: white;
   border:0px;
}

.action-icon {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-frame {
  width: 24px;
  height: 24px;
}

.icon {
  width: 100%;
  height: 100%;
}

.text {
  font-size: 16px;
  font-weight: 400;
  color: #222222;
}

.action-button:disabled {
  background-color: #e0e0e0;
  color: #b0b0b0;
  cursor: not-allowed;
}



/////////////////////////////////////////////////////////
/// 
/// 
/// 
.form-fields-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* ثابت: فقط عمودين */
  gap: 16px;
  margin-bottom: 24px;
}


.form-fields-grid > *:nth-last-child(1):nth-child(odd) {
  grid-column: span 2;
}


.form-field {
  display: flex;
  flex-direction: column;
}


/* حاوية الحقل المتعدد */
.is-multi .multi-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* الحقل نفسه */
.is-multi .multi-input {
  flex: 1;
}

/* الأزرار */
.is-multi .multi-buttons {
  display: flex;
  flex-direction: row;
  gap: 4px;
  flex-shrink: 0;
}

.remove-button, .add-button {
  //padding: 6px 8px;
  font-size: 12px;
  cursor: pointer;
}


.invisible {
  visibility: hidden;
}

.horizontal-container {
  display: flex;
  justify-content: space-between; /* pushes items to left and right */
  align-items: center;            /* vertically align items */
  width: 100%;
}

