import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MetadataService } from '../metadata.service';

@Component({
  selector: 'app-submenu',
  templateUrl: './submenu.component.html',
  styleUrls: ['./submenu.component.scss'],
  standalone: true,
  imports: [
    MatIconModule,
    CommonModule
  ]
})
export class SubmenuComponent {
  @Input() menuItem: any;
  @Input() level: number = 1;
  @Output() onMenuSelected = new EventEmitter<any>();

  submenuItems: any[] = [];
  expanded = false;
  loaded = false;
  loading = false;

  constructor(private metadataService: MetadataService) {}

  toggleSubmenu(event: Event) {
    event.stopPropagation(); // Stop event propagation to avoid triggering parent actions
    if (this.menuItem.type === 'menu') { // Only toggle if it's a menu type
      this.expanded = !this.expanded;

      if (this.expanded && !this.loaded) {
        this.loadSubmenu();
      }
    } else {
      this.selectMenuItem(this.menuItem); // Call selectMenuItem if it's not a 'menu' type
    }
  }

  loadSubmenu() {
    this.loading = true;
    this.metadataService.getMenu(this.menuItem.application).subscribe(
      (response) => {
        this.submenuItems = response?.menus || [];
        this.loaded = true;
        this.loading = false;
      },
      (error) => {
        console.error(`Error loading submenu for ${this.menuItem.application}:`, error);
        this.loading = false;
      }
    );
  }

  selectMenuItem(menuItem: any) {
    this.onMenuSelected.emit(menuItem); // Emit the selected menu item for all types
  }

  getIcon(menuType: string): string {
    switch (menuType) {
      case 'menu':
        return 'list';
      case 'qur':
        return 'query_stats';
      case 'scr':
        return 'screen_share';
      case 'table':
        return 'table_chart';
      default:
        return 'info';
    }
  }
}
